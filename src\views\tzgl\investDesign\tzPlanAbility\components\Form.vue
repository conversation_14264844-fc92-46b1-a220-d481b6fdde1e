<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="200px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="年份" prop="tpaYear">
            <el-date-picker
              v-model="form.tpaYear"
              type="year"
              style="width: 100%;"
              value-format="yyyy"
              placeholder="选择年">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="单位ID" prop="tpaOdId" style="display:none;">
            <el-input v-model.trim="form.tpaOdId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作者ID" prop="tpaOeId" style="display:none;">
            <el-input v-model.trim="form.tpaOeId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作者" prop="tpaOeName" style="display:none;">
            <el-input v-model.trim="form.tpaOeName" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资计划执行率" prop="tpaRate" >
            <el-input-number v-model.trim="form.tpaRate" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="tpaStatus" style="display:none;">
            <el-input v-model.trim="form.tpaStatus" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资队伍建设(人数)" prop="tpaTeam">
            <el-input-number v-model.trim="form.tpaTeam" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="tpaId"  style="display:none;">
            <el-input v-model.trim="form.tpaId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanAbilityForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>