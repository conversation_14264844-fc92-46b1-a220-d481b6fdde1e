import request from '@/utils/request'

// 分页查询参股企业治理信息表（整改相关）
export function getCorrectionList(query) {
  return request({
    url: '/zc-cggq-company-governance/vPageList',
    method: 'get',
    params: query
  })
}

// 新增参股企业治理信息（整改）
export function addCorrectionInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/save',
    method: 'post',
    data: data
  })
}

// 修改参股企业治理信息（整改）
export function updateCorrectionInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 删除参股企业治理信息（整改）
export function deleteCorrectionInfo(ids) {
  return request({
    url: '/zc-cggq-company-governance/delete',
    method: 'post',
    data: { ids: ids }
  })
}

// 获取单条记录详情
export function getCorrectionDetail(id) {
  return request({
    url: `/zc-cggq-company-governance/get-by-id/${id}`,
    method: 'get'
  })
}

// 批量更新整改状态
export function batchUpdateCorrectionStatus(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 导出整改数据
export function exportCorrectionData(query) {
  return request({
    url: '/zc-cggq-company-governance/vExport',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
