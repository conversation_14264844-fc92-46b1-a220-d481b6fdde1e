import request from '@/utils/request'

// 分页查询参股企业治理信息表
export function getCompanyGoverList(params) {
  return request({
    url: '/zc-cggq-company-governance/vPageList',
    method: 'get',
    params
  })
}

// 新增参股企业治理信息
export function addCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/save',
    method: 'post',
    data
  })
}

// 修改参股企业治理信息
export function updateCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data
  })
}

// 新增或修改参股企业治理信息(带日志)
export function saveOrUpdateCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/saveOrUpdLog',
    method: 'post',
    data
  })
}

// 删除参股企业治理信息(通过ID)
export function deleteCompanyGoverById(id) {
  return request({
    url: `/zc-cggq-company-governance/delete-by-id/${id}`,
    method: 'delete'
  })
}

// 删除参股企业治理信息(带日志)
export function deleteCompanyGoverLog(data) {
  return request({
    url: '/zc-cggq-company-governance/delete-by-id-log',
    method: 'post',
    data
  })
}

// 通过id查询参股企业治理信息
export function getCompanyGoverById(id) {
  return request({
    url: `/zc-cggq-company-governance/get-by-id/${id}`,
    method: 'get'
  })
}

// 参股企业分析 - 生成测试数据
export function generateCompanyGoverTestData() {
  return request({
    url: '/zc-cggq-company-governance/generate-test-data',
    method: 'post'
  })
}

// 批量更新流程阶段状态
export function batchUpdateProcessStage(params) {
  return request({
    url: '/zc-cggq-company-governance/batch-update-process-stage',
    method: 'post',
    params
  })
}

// 导出参股企业治理信息
export function exportCompanyGover(data) {
  return request({
    url: '/zc-cggq-company-governance/vExport',
    method: 'post',
    data
  })
}

// 提交建议
export function submitSuggestion(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: {
      ...data,
      logDesc: `提交建议：${data.zccgHoldingEnterpriseName || '参股企业'}`
    }
  })
}

// 提交修正
export function submitCorrection(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: {
      ...data,
      logDesc: `修正信息：${data.zccgHoldingEnterpriseName || '参股企业'}`
    }
  })
}

// 获取回退计划列表
export function getBackPlanList(params) {
  return request({
    url: '/zc-cggq-company-governance/vPageList',
    method: 'get',
    params
  })
}

// 提交回退计划建议
export function submitBackPlanSuggestion(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: {
      ...data,
      logDesc: `回退计划建议：${data.zccgHoldingEnterpriseName || '参股企业'}`
    }
  })
}

// 提交回退计划修正
export function submitBackPlanCorrection(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: {
      ...data,
      logDesc: `回退计划修正：${data.zccgHoldingEnterpriseName || '参股企业'}`
    }
  })
}
