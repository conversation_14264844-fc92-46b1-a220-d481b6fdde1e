<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="12">
          <el-form-item label="现金分红比率" prop="tpltDividendRatio">
            <el-input-number v-model.trim="form.tpltDividendRatioStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltDividendRatioEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司市值(亿元)" prop="tpltMarketValue">
            <el-input-number v-model.trim="form.tpltMarketValueStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltMarketValueEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位ID" prop="tpltOdId">
            <el-input v-model.trim="form.tpltOdId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作者ID" prop="tpltOeId">
            <el-input v-model.trim="form.tpltOeId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作者" prop="tpltOeName">
            <el-input v-model.trim="form.tpltOeName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司基本每股收益(EPS)年增长率" prop="tpltPriceEps">
            <el-input-number v-model.trim="form.tpltPriceEpsStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltPriceEpsEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市值排名(申万一级行业或恒生综合行业前%)" prop="tpltPriceRank">
            <el-input-number v-model.trim="form.tpltPriceRankStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltPriceRankEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="股价相对综合增长率" prop="tpltPriceRate">
            <el-input-number v-model.trim="form.tpltPriceRateStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltPriceRateEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上市公司市净率(是否破净)" prop="tpltPriceRatio">
            <el-input-number v-model.trim="form.tpltPriceRatioStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltPriceRatioEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构投资者占比" prop="tpltProportion">
            <el-input-number v-model.trim="form.tpltProportionStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltProportionEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证券交易所信息披露质量等级" prop="tpltQualityRating">
            <el-input-number v-model.trim="form.tpltQualityRatingStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpltQualityRatingEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="tpltStatus">
            <el-input v-model.trim="form.tpltStatus" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年度" prop="tpltYear">
            <el-input v-model.trim="form.tpltYear" clearable ></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanListedTargetQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>