<!--修正弹窗-->
<template>
  <BaseDialog
    title="修正"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <el-form ref="correctForm" :model="formData" :rules="formRules" label-width="100px" class="correct-form">
      <el-form-item label="修正结果" prop="correctResult">
        <el-select
          v-model="formData.correctResult"
          placeholder="请选择修正结果"
          style="width: 100%"
        >
          <el-option label="关注" value="黄灯"></el-option>
          <el-option label="清退" value="红灯"></el-option>
          <el-option label="保留" value="绿灯"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否保留" prop="isKeep">
        <el-select
          v-model="formData.isKeep"
          placeholder="请选择是否保留"
          style="width: 100%"
        >
          <el-option label="否" value="否"></el-option>
          <el-option label="是" value="是"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "correctDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        correctResult: '',
        isKeep: '',
        correctionNote: ''
      },
      formRules: {
        correctResult: [
          { required: true, message: '请选择修正结果', trigger: 'change' }
        ],
        isKeep: [
          { required: true, message: '请选择是否保留', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        correctResult: this.initialData.zccgStatus || '',
        isKeep: this.initialData.zccgIsProtected || '',
        correctionNote: ''
      }
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.correctForm) {
          this.$refs.correctForm.clearValidate()
        }
      })
    },

    // 确认提交
    handleConfirm() {
      this.$refs.correctForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.enterprise-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.enterprise-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.enterprise-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.correct-form {
  padding: 0;
}

.correct-form .el-form-item {
  margin-bottom: 20px;
}
</style>
