<template>
  <el-form ref="form" :model="queryForm" label-width="110px" :rules="rules">
    <el-row>
      <el-col :span="24">
        <el-form-item label="单位" prop="ttelDept">
          <el-input v-model="queryForm.ttelDept" maxlength="100"  @focus="handleFocus"  placeholder="请输入单位"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="联系人姓名" prop="ttelLinkName">
          <el-input v-model="queryForm.ttelLinkName" maxlength="100"  show-word-limit  placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="联系方式" prop="ttelLinkPhone">
          <el-input v-model="queryForm.ttelLinkPhone" maxlength="100" show-word-limit  placeholder="请输入手机号或固定电话"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <<DialogCard
      ref="dialogCard"
      :dialogTableVisible="deptDialogVisible"
      v-if="deptDialogVisible"
      :close="closeDeptDialigCard"
      title="数据选择"
      :flag="true"
      width="50%"
      top="15vh">
      <DepartmentTable ref="DepartmentTable" slot="content" />
      <template #footer>
        <el-button @click="closeDeptDialigCard">取消</el-button>
        <el-button type="primary" @click="handleChangeClick">确定</el-button>
      </template>
    </DialogCard>
  </el-form>
</template>

<script>
  import DialogCard from 'common/DialogCard'
  import DepartmentTable from 'common/DepartmentTable'

  export default {
    name: 'InsideMarketForm',
    props: {
      queryForm: {
        type: Object,
        default: () => {
          return {}
        }
      },
    },
    components: {
      DialogCard,
      DepartmentTable
    },  
    data(){
      return {
        deptDialogVisible: false,
        rules: {
          ttelDept: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          ttelLinkName: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          ttelLinkPhone: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
            // 正则校验：支持手机号(11位数字)或固定电话(带区号的格式)
            { 
              pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, 
              message: '请输入正确的手机号或固定电话', 
              trigger: 'blur' 
            }
          ],
        },
      }
    },
    methods: {
      handleFocus() {
        this.deptDialogVisible = true
      },
      closeDeptDialigCard() {
        this.deptDialogVisible = false
      },
      handleChangeClick(){
        this.$set(this.queryForm, 'ttelDept', this.$refs.DepartmentTable.row.odName)
        this.closeDeptDialigCard()
        // this.$set(this.queryForm, 'ttelDeptId', this.$refs.DepartmentTable.row.odId)
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>