<template>
  <VabTable 
    :tableHeader="tableHeader" 
    :tableData="tableData" 
    :tableHeight="tableHeight"
    :pageNo="tablePage.pageNo"
    :pageSize="tablePage.pageSize"
    :total="tablePage.total"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script>
  import SmallCard from 'common/SmallCard';
  import VabTable from 'components/VabTable';

  export default {
    name: 'BoostProject',
    components: {
      SmallCard,
      VabTable
    },
    data() {
      return {
        radioOptions:[
          {label:'固定资产投资'},
          {label:'股权投融资'},
          {label:'资产经营'},
        ],
        tableHeader: [
          { type: 'index', width:'55', label: "序号",align:'center' }, 
          { prop: 'ttelDept', label:'流程阶段',align:'center' },
          { prop: 'ttelDept', label:'项目名称',align:'center' },
          { prop: 'ttelDept', label:'起草时间',align:'center' },
          { prop: 'ttelDept', label:'起草人',align:'center' },
        ],
        tableData: [],
        tableHeight: this.$baseTableHeight(1, 1) / 2 -32, 
        tablePage: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        }
      }
    },
    created(){

    },
    methods:{
      handleSizeChange(val) {
        this.tablePage.pageSize = val
      },
      handleCurrentChange(val) {
        this.tablePage.pageNo = val
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>