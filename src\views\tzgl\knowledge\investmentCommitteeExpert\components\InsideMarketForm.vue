<template>
  <el-form ref="form" :model="queryForm" label-width="110px" :rules="rules">
     <el-row>
      <el-col :span="12">
        <el-form-item label="姓名" prop="tteName">
          <el-input v-model="queryForm.tteName" maxlength="100"  show-word-limit  placeholder="请输入姓名"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="年龄" prop="tteAge">
          <el-input 
            v-model="queryForm.tteAge" 
            maxlength="3"  
            show-word-limit  
            placeholder="请输入年龄"
            @input="handleAgeInput"
            type="text"
            inputmode="numeric"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="政治面貌" prop="tteZzmm">
           <el-select v-model="queryForm.tteZzmm"  style="width: 100%;" placeholder="请选择政治面貌">
             <el-option
               v-for="item in zzmmOptions"
               :key="item.LPV_ID"
               :label="item.LPV_NAME"
               :value="item.LPV_NAME">
             </el-option>
           </el-select>
         </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="推荐单位" prop="tteTjdw">
          <el-input v-model="queryForm.tteTjdw" maxlength="100"  show-word-limit  placeholder="请输入推荐单位"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="任职单位" prop="tteRzdw">
          <el-input v-model="queryForm.tteRzdw" maxlength="100"  show-word-limit  placeholder="请输入任职单位"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="行政职务" prop="tteYzzw">
          <el-input v-model="queryForm.tteYzzw" maxlength="100"  show-word-limit  placeholder="请输入行政职务"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="专业技术职务" prop="tteZyjszw">
          <el-input v-model="queryForm.tteZyjszw" maxlength="100"  show-word-limit  placeholder="请输入专业技术职务"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="专业领域" prop="tteZyly">
          <el-input v-model="queryForm.tteZyly" maxlength="100"  show-word-limit  placeholder="请输入专业领域"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="业务类别" prop="tteYwlb">
          <el-input v-model="queryForm.tteYwlb" maxlength="100"  show-word-limit  placeholder="请输入业务类别"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="一级功能域" prop="tteYjgny">
          <el-input v-model="queryForm.tteYjgny" maxlength="100"  show-word-limit  placeholder="请输入一级功能域"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="全日制学历" prop="tteEdu">
           <el-select v-model="queryForm.tteEdu"  style="width: 100%;" placeholder="请选择全日制学历">
             <el-option
               v-for="item in qrzOptions"
               :key="item.LPV_ID"
               :label="item.LPV_NAME"
               :value="item.LPV_NAME">
             </el-option>
           </el-select>
         </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="毕业院校" prop="tteGraduate">
          <el-input v-model="queryForm.tteGraduate" maxlength="100"  show-word-limit  placeholder="请输入毕业院校"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="专业" prop="tteMajor">
          <el-input v-model="queryForm.tteMajor" maxlength="100"  show-word-limit  placeholder="请输入专业"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="在职教育" prop="tteZzEdu">
          <el-input v-model="queryForm.tteZzEdu" maxlength="100"  show-word-limit  placeholder="请输入在职教育"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="毕业院校" prop="tteZzGraduate">
          <el-input v-model="queryForm.tteZzGraduate" maxlength="100"  show-word-limit  placeholder="请输入毕业院校"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="专业" prop="tteZzMajor">
          <el-input v-model="queryForm.tteZzMajor" maxlength="100"  show-word-limit  placeholder="请输入专业"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

  export default {
    name: 'InsideMarketForm',
    props: {
      queryForm: {
        type: Object,
        default: () => {
          return {
            tteAge: '' // 确保初始值为字符串，便于处理
          }
        }
      },
      zzmmOptions: {
        type: Array,
        default: () => {
          return []
        }
      },
      qrzOptions: {
        type: Array,
        default: () => {
          return []
        }
      },
    },
    data(){
      return {
        rules: {
          tteName: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteAge: [
            { required: true, message: '此项为必填项', trigger: 'blur' },
            { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' },
            { 
              validator: (rule, value, callback) => {
                // 将字符串转换为数字进行验证
                const num = parseInt(value, 10);
                if (num < 1) {
                  callback(new Error('年龄不能小于1岁'));
                } else if (num > 120) {
                  callback(new Error('年龄不能大于120岁'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          tteZzmm:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteTjdw:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteRzdw:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteYzzw:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteZyjszw:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteZyly:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteYwlb:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteYjgny:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteEdu:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteGraduate:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteMajor:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteZzEdu:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteZzGraduate:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ],
          tteZzMajor:[
            { required: true, message: '此项为必填项', trigger: 'blur' },
          ]
        },
      }
    },
    methods: {
      // 处理年龄输入，确保只能输入数字
      handleAgeInput(value) {
        // 移除所有非数字字符
        if (value || value === 0) {
          const numericValue = value.toString().replace(/[^0-9]/g, '');
          // 避免输入过长数字
          if (numericValue.length > 3) {
            this.$set(this.queryForm, 'tteAge', numericValue.slice(0, 3));
          } else {
            this.$set(this.queryForm, 'tteAge', numericValue);
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
    