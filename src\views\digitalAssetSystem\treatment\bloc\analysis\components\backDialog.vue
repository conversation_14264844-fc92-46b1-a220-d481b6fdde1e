<!--选择回退计划弹窗-->
<template>
  <BaseDialog
    title="选择回退计划"
    :visible.sync="dialogVisible"
    type="edit"
    size="Max"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="back-dialog-content">
      <!-- 顶部筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="10">
          <el-col :span="3.8">
            <el-input v-model="filterForm.planType" placeholder="二级成员单位" clearable></el-input>
          </el-col>
          <el-col :span="3.8">
            <el-input v-model="filterForm.enterprise" placeholder="参股企业名称" clearable></el-input>
          </el-col>
          <el-col :span="1">
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </el-col>
          <el-col :span="1">
            <el-button @click="handleCancel">回退</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          border
          stripe
          height="400"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          row-key="zccgId"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="zccgSecondLevelUnit" label="二级成员单位" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgInvestmentEntity" label="投资主体" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgHoldingEnterpriseName" label="参股企业名称" width="150" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgFirstTimeForGroup" label="我方是否为第一大股东（集团合计）" width="180" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgHasActualController" label="是否有实际控制人" width="140" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgDividendOverYears" label="是否满5年未分红" width="140" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgIsLongTermLoss" label="是否长期亏损" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgIsLowEfficiencyAsset" label="是否属于低效无效资产" width="160" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgIsNonContinuingOperation" label="是否非持续经营" width="140" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgTimelyDisclosureMajorMatters" label="是否能及时掌握财务数据和经营情况" width="200" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgLiquidationYear" label="清退年份" width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgStatus" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <div class="status-indicator">
                <span
                  class="status-dot"
                  :class="getStatusClass(scope.row.zccgStatus)"
                ></span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="zccgSecondLevelUnitSuggestion" label="分析建议" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgComprehensiveReasons" label="整改建议" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgIsProtected" label="是否保留" width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="zccgIsCompliantExit" label="是否完成清退" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleSuggestion(scope.row)">建议</el-button>
              <el-button type="text" size="small" @click="handleCorrection(scope.row)">修正</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          background
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'
import {
  getBackPlanList,
  submitBackPlanSuggestion,
  submitBackPlanCorrection
} from '@/api/digitalAssetSystem/treatment/analysis.js'

export default {
  name: "backDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      filterForm: {
        planType: '',
        enterprise: ''
      },
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        zccgSecondLevelUnit: '', // 二级成员单位
        zccgHoldingEnterpriseName: '' // 企业名称
      },
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableData: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadData()
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog() {
      this.dialogVisible = true
    },

    // 重置表单
    resetForm() {
      this.filterForm = {
        planType: '',
        enterprise: ''
      }
      this.selectedRows = []
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'urgent': 'status-urgent',    // 红色
        'safe': 'status-safe',        // 黄色
        'normal': 'status-normal'     // 绿色
      }
      return statusMap[status] || 'status-normal'
    },

    // 加载数据
    loadData() {
      this.loading = true

      // 构建查询参数
      const params = {
        ...this.searchForm,
        pageNo: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }

      // 如果有筛选条件，添加到查询参数中
      if (this.filterForm.planType) {
        params.zccgSecondLevelUnit = this.filterForm.planType
      }
      if (this.filterForm.enterprise) {
        params.zccgHoldingEnterpriseName = this.filterForm.enterprise
      }

      getBackPlanList(params).then(response => {
        if (response && response.data) {
          // 将API返回的数据转换为表格需要的格式
          this.tableData = this.transformApiData(response.data.list || [])
          this.pagination.total = response.data.total || 0
        } else {
          // 如果接口暂时没有数据，使用模拟数据展示表格效果
          this.tableData = this.getMockData()
          this.pagination.total = this.tableData.length
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        // 接口调用失败时使用模拟数据
        this.tableData = this.getMockData()
        this.pagination.total = this.tableData.length
        console.warn('接口调用失败，使用模拟数据')
      })
    },

    // 转换API数据为表格格式
    transformApiData(apiData) {
      return apiData.map(item => ({
        // 直接使用API返回的字段名，与analysis/index.vue保持一致
        zccgId: item.zccgId,
        zccgSecondLevelUnit: item.zccgSecondLevelUnit,
        zccgInvestmentEntity: item.zccgInvestmentEntity,
        zccgHoldingEnterpriseName: item.zccgHoldingEnterpriseName,
        zccgFirstTimeForGroup: item.zccgFirstTimeForGroup,
        zccgHasActualController: item.zccgHasActualController,
        zccgDividendOverYears: item.zccgDividendOverYears,
        zccgIsLongTermLoss: item.zccgIsLongTermLoss,
        zccgIsLowEfficiencyAsset: item.zccgIsLowEfficiencyAsset,
        zccgIsNonContinuingOperation: item.zccgIsNonContinuingOperation,
        zccgTimelyDisclosureMajorMatters: item.zccgTimelyDisclosureMajorMatters,
        zccgLiquidationYear: item.zccgLiquidationYear,
        zccgStatus: item.zccgStatus,
        zccgSecondLevelUnitSuggestion: item.zccgSecondLevelUnitSuggestion,
        zccgComprehensiveReasons: item.zccgComprehensiveReasons,
        zccgIsProtected: item.zccgIsProtected,
        zccgIsCompliantExit: item.zccgIsCompliantExit
      }))
    },

    // 获取状态样式类（与analysis/index.vue保持一致）
    getStatusClass(status) {
      const statusMap = {
        '红灯': 'status-urgent',    // 红色
        '黄灯': 'status-safe',        // 黄色
        '绿灯': 'status-normal'     // 绿色
      }
      return statusMap[status] || 'status-normal'
    },

    // 获取模拟数据
    getMockData() {
      return [
        {
          zccgId: 1,
          zccgSecondLevelUnit: '中电科技集团',
          zccgInvestmentEntity: '中电科技',
          zccgHoldingEnterpriseName: '参股企业A',
          zccgFirstTimeForGroup: '是',
          zccgHasActualController: '是',
          zccgDividendOverYears: '否',
          zccgIsLongTermLoss: '否',
          zccgIsLowEfficiencyAsset: '否',
          zccgIsNonContinuingOperation: '否',
          zccgTimelyDisclosureMajorMatters: '是',
          zccgLiquidationYear: '2025',
          zccgStatus: '关注',
          zccgSecondLevelUnitSuggestion: '建议保留',
          zccgComprehensiveReasons: '经营状况良好',
          zccgIsProtected: '是',
          zccgIsCompliantExit: '否'
        },
        {
          zccgId: 2,
          zccgSecondLevelUnit: '电子集团',
          zccgInvestmentEntity: '电子科技',
          zccgHoldingEnterpriseName: '参股企业B',
          zccgFirstTimeForGroup: '否',
          zccgHasActualController: '否',
          zccgDividendOverYears: '是',
          zccgIsLongTermLoss: '是',
          zccgIsLowEfficiencyAsset: '是',
          zccgIsNonContinuingOperation: '是',
          zccgTimelyDisclosureMajorMatters: '否',
          zccgLiquidationYear: '2025',
          zccgStatus: '清退',
          zccgSecondLevelUnitSuggestion: '建议清退',
          zccgComprehensiveReasons: '长期亏损，建议清退',
          zccgIsProtected: '否',
          zccgIsCompliantExit: '是'
        }
      ]
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      // 将筛选条件同步到搜索表单
      this.searchForm.zccgSecondLevelUnit = this.filterForm.planType
      this.searchForm.zccgHoldingEnterpriseName = this.filterForm.enterprise
      this.loadData()
    },

    // 重置
    handleReset() {
      this.resetForm()
      // 清空搜索表单
      this.searchForm.zccgSecondLevelUnit = ''
      this.searchForm.zccgHoldingEnterpriseName = ''
      this.pagination.currentPage = 1
      this.loadData()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 行点击
    handleRowClick(row) {
      console.log('点击行:', row)
    },

    // 建议
    handleSuggestion(row) {
      console.log('建议:', row)
      this.$emit('suggestion', row)
    },

    // 修正
    handleCorrection(row) {
      console.log('修正:', row)
      this.$emit('correction', row)
    },

    // 详情
    handleDetail(row) {
      console.log('详情:', row)
      this.$emit('detail', row)
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
      this.resetForm()
    },

    // 确定
    handleConfirm() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择至少一条记录')
        return
      }
      this.$emit('confirm', this.selectedRows)
      this.dialogVisible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped lang="scss">
.back-dialog-content {
  .filter-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .table-section {
    margin-bottom: 20px;
  }

  .pagination-section {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .status-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-urgent {
    background-color: #f56c6c; /* 红色 */
  }

  .status-safe {
    background-color: #e6a23c; /* 黄色 */
  }

  .status-normal {
    background-color: #67c23a; /* 绿色 */
  }
}


/* 表格样式优化 */
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

::v-deep .el-button--text {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}
</style>
