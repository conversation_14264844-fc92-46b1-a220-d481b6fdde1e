<template>
  <div class="investment-committee-expert">
    <VabForm :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%"/>
    <VabTable 
      :tableHeader="tableHeader" 
      :tableData="tableData" 
      :tableHeight="tableHeight"
      :pageNo="tablePage.pageNo"
      :pageSize="tablePage.pageSize"
      :total="tablePage.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      v-if="dialogVisible"
      :close="closeDialigCard"
      :title='title'
      :flag="true"
      height="80%"
      width="50%"
      top="15vh">
      <ContactForm slot="content" ref="contactForm" :queryForm="queryForm"/>
      <template #footer>
        <el-button @click="closeDialigCard">取消</el-button>
        <el-button type="primary" @click="handleSaveClick">确定</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import CardBox from 'common/CardBox'
  import VabTable from 'components/VabTable'
  import VabForm from 'components/VabForm'
  import DialogCard from 'common/DialogCard'
  import ContactForm from './ContactForm.vue'
  import { getLinkUserData, saveLinkUserData, deleteLinkUserData, } from 'api/tzgl/project/investmentCommitteeExpert';
  import { cloneDeep } from 'lodash'

  export default {
    name:'investmentCommitteeExpert',
    components: {
      CardBox,
      VabTable,
      VabForm,
      DialogCard,
      ContactForm,
    },
    props: {
      activeName: {
        type: String,
        default: '0'
      },
      parentData: {
        type: Object,
        default: ()=>{}
      }
    },
    data(){
      return {
        searchForm: {},
        formItem: [
          {name: 'input', prop: 'zlmName', label: '模糊查询', placeholder: '请输入标签名称'},
          {name: 'input', prop: 'ttelDept', label: '单位', placeholder: '请输入标签名称'},
          {name: 'input', prop: 'ttelLinkName', label: '姓名', placeholder: '请输入标签名称'},
          {name: 'button', prop: 'button', label: '查询', type: "primary", click: this.searchTableData},
          {name: 'button', prop: 'button', label: '重置', click: this.handleResetFun},
          {name: 'button', prop: 'button', label: '添加', click: this.handleAddClick},
        ],
        tableHeader: [
          { type: 'index', width:'55', label: "序号",align:'center' }, 
          { prop: 'ttelDept', label:'单位',align:'center' },
          { prop: 'ttelLinkName', label:'联系人姓名',align:'center' },
          { prop: 'ttelLinkPhone', label:'联系方式',align:'center' },
          { type: 'action', width: '100', label: "操作", align:'center', render: (h, scope) => {
            return (
              <div>
                <el-button type="text" size="small" onClick={() => this.handleEdit(scope.row)}>编辑</el-button>
                <el-button type="text" size="small" onClick={() => this.handleDelete(scope.row)}>删除</el-button>
              </div>
            )
          }},
        ],
        tableData: [],
        tableHeight: this.$baseTableHeight(1, 1) - 46, 
        tablePage: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        dialogVisible: false,
        title:'新增',
        queryForm: {},
      }
    },
    created(){
      this.getTableData();
    },
    methods: {
      async searchTableData() {
        this.getTableData();
      },
      handleResetFun(){
        this.searchForm = {};
        this.getTableData();
      },
      async getTableData() {
        const {data:{list,total},code,msg} = await getLinkUserData(this.searchForm);
        if(code == 200){
          this.tableData = list;
          this.tablePage.total = total;  
        }else{
          this.$message.error(msg);
        }
      },
      // 添加
      handleAddClick(){
        this.dialogVisible = true;
        this.title = '新增';
        this.queryForm = {};
      },
      // 编辑
      handleEdit(row){
        this.dialogVisible = true;
        this.title = '编辑';
        this.queryForm = cloneDeep(row);
      },
      // 保存函数
      handleSaveClick(){
        this.$refs['contactForm'].$refs.form.validate(async (valid) => {
          if (valid) {
             const {data,code,msg} = await saveLinkUserData(this.queryForm);
            if(code == 200){
              this.$message.success('保存成功');  
              this.dialogVisible = false;
              this.getTableData();
            }else{
              this.$message.error(msg);
            }
          }
        })
      },
      // 删除
      handleDelete(row){
        this.$baseConfirm('确定删除吗', null, async () => {
          const msg = await deleteLinkUserData(row)
          if(msg.code == 200) {
            this.$message({message:'删除操作成功!',type:'success'})
            this.getTableData();
          }else{
            this.$message({message:'删除操作失败!',type:'warning'})
          }
        })
      },
      closeDialigCard(){
        this.dialogVisible = false;
        this.queryForm = {};
      },
      handleSizeChange(val) {
        this.tablePage.pageSize = val
      },
      handleCurrentChange(val) {
        this.tablePage.pageNo = val
      },
    }
  }
</script>

<style lang="sass" scoped>

</style>