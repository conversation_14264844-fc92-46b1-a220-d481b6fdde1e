<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="24">
          <el-form-item label="重点投资方向" prop="tptDirection">
            <el-input v-model.trim="form.tptDirection" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资分类" prop="tptSort">
            <el-select v-model.trim="form.tptSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.tptSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="计划投资总金额" prop="tptAmount">
            <el-input-number v-model.trim="form.tptAmountStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmountEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第一年计划投资金额" prop="tptAmount0">
            <el-input-number v-model.trim="form.tptAmount0Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount0End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第二年计划投资金额" prop="tptAmount1">
            <el-input-number v-model.trim="form.tptAmount1Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount1End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第三年计划投资金额" prop="tptAmount2">
            <el-input-number v-model.trim="form.tptAmount2Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount2End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第四年计划投资金额" prop="tptAmount3">
            <el-input-number v-model.trim="form.tptAmount3Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount3End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第五年计划投资金额" prop="tptAmount4">
            <el-input-number v-model.trim="form.tptAmount4Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount4End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第六年计划投资金额" prop="tptAmount5">
            <el-input-number v-model.trim="form.tptAmount5Start" clearable></el-input-number> - <el-input-number v-model.trim="form.tptAmount5End" clearable></el-input-number>
          </el-form-item>
        </el-col>
        
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanTargetsQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          tptSort:[]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const tptSort = await getSysValList({ "lpvLpdId": "INVESTSORTS" });
        if(tptSort.code==200){
          this.getOptionsData(tptSort.data,"tptSort");
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>