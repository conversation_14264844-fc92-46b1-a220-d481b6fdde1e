<!--建议弹窗-->
<template>
  <BaseDialog
    title="建议"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <el-form ref="suggestionForm" :model="formData" class="suggestion-form">
      <el-form-item label="建议内容" prop="suggestion">
        <el-input
          v-model="formData.suggestion"
          type="textarea"
          :rows="6"
          placeholder="请输入建议内容"
          maxlength="500"
          show-word-limit
          resize="none"
        />
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "suggestionDialog",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始建议内容
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        suggestion: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        suggestion: this.initialData.suggestion || ''
      }
    },

    // 确认提交
    handleConfirm() {
      // 验证建议内容不能为空
      if (!this.formData.suggestion.trim()) {
        this.$message.warning('请输入建议内容')
        return
      }

      this.$emit('confirm', {
        suggestion: this.formData.suggestion.trim()
      })
      this.dialogVisible = false
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.enterprise-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.enterprise-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.enterprise-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.suggestion-form {
  padding: 0;
}

.suggestion-form .el-form-item {
  margin-bottom: 0;
}

.suggestion-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}
</style>
