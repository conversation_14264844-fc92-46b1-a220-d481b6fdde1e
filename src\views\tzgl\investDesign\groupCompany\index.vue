<template>
  <el-row :gutter="20">
    <el-col :span="8">
      <CardBox title="二级单位列表">

      </CardBox>
    </el-col>
    <el-col :span="16">
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleTabsClick">
        <el-tab-pane label="一、投资规划目标" name="first">
          <tzPlanTargets />
        </el-tab-pane>
        <el-tab-pane label="二、融资规划目标" name="second">
          <rzPlanTargets />
        </el-tab-pane>
        <el-tab-pane label="三、上市公司市值管理目标" name="third">
          <tzPlanListedTarget />
        </el-tab-pane>
        <el-tab-pane label="四、投资能力体系规划" name="fourth">
          <tzPlanAbility />
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script>
  import CardBox from 'common/CardBox'
  import tzPlanTargets from '../tzPlanTargets'
  import rzPlanTargets from '../rzPlanTargets'
  import tzPlanListedTarget from '../tzPlanListedTarget'
  import tzPlanAbility from '../tzPlanAbility'
  export default {
    name: "GroupCompany",
    components: {
      CardBox,
      tzPlanTargets,
      rzPlanTargets,
      tzPlanListedTarget,
      tzPlanAbility
    },
    data() {
      return {
        activeName: 'first'
      }
    },
    methods: {
      handleTabsClick() {

      }
    },
  }
</script>

<style lang="scss" scoped>

</style>