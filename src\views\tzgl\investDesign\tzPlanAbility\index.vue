<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanAbilitySearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzPlanAbilityTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

    <el-table
      ref="tzPlanAbilityTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanAbility"
      row-key="tpaId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="85"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="50%"
      height="530px"
    >
      <tzPlanAbilityForm
        ref="tzPlanAbilityForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>


  </div>
</template>

<script>
  import { 
    tzPlanAbilityDoDeleteELog,
    tzPlanAbilityGetList,
    tzPlanAbilityDoSaveOrUpdLog,
    tzPlanAbilityDoExport 
  } from '@/api/tzgl/investDesign/tzPlanAbility'
  import DialogCard from 'common/DialogCard'
  import tzPlanAbilitySearch from './components/Search.vue'
  import tzPlanAbilityForm from './components/Form.vue'
  import tzPlanAbilityQuery from './components/Query.vue'
  import { exportRearEnd } from '@/api/exportExcel'
  import { baseURL } from '@/config'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanAbility',
    components: {
      DialogCard,
      tzPlanAbilitySearch,
      tzPlanAbilityForm,
      tzPlanAbilityQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tpaRate: [
            { required: true, message: '请输入投资计划执行率', trigger: 'blur' }
          ],
          // tpaStatus: [
          //   { required: true, message: '请输入状态', trigger: 'blur' }
          // ],
          tpaTeam: [
            { required: true, message: '请输入投资队伍建设(人数)', trigger: 'blur' }
          ],
          tpaYear: [
            { required: true, message: '请输入年份', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: !this.gheight?this.$baseTableHeight(1,1):this.gheight,
        checkList: ['投资计划执行率','投资队伍建设(人数)','年份', '单位'],
        columns: [
          { prop:'tpaYear', label:'年份', width:'auto'   },
          { prop:'tpaOdName', label:'单位', width:'auto'   },
          { prop:'tpaOdId', label:'单位ID', width:'auto'   },
          { prop:'tpaOeId', label:'操作者ID', width:'auto'   },
          { prop:'tpaOeName', label:'操作者', width:'auto'   },
          { prop:'tpaRate', label:'投资计划执行率', width:'auto'   },
          { prop:'tpaStatus', label:'状态', width:'auto'   },
          { prop:'tpaTeam', label:'投资队伍建设(人数)', width:'auto'   },
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanAbilityForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await tzPlanAbilityDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={tpaOdId:this.loginUser.odId,tpaOeId:this.loginUser.oeId,tpaOeName:this.loginUser.oeName,tpaStatus:''}
        this.editType = 'add'
        this.title = '添加'
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tpaId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzPlanAbilityDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询弹框
      handleQuery() {
        this.queryForm = Object.assign(this.queryForm,this.searchForm)
        this.$refs['tzPlanAbilityQuerySearch'].showQuery('查询')
      },
      //高级查询关闭
      queryClose(){
        this.$refs.tzPlanAbilityQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.tzPlanAbilityQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await tzPlanAbilityGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>