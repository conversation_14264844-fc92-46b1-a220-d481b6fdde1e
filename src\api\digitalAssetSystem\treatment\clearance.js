import request from '@/utils/request'

// 分页查询参股企业治理信息表（清退相关）
export function getClearanceList(query) {
  return request({
    url: '/zc-cggq-company-governance/vPageList',
    method: 'get',
    params: query
  })
}

// 新增参股企业治理信息
export function addClearanceInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/save',
    method: 'post',
    data: data
  })
}

// 修改参股企业治理信息
export function updateClearanceInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 删除参股企业治理信息
export function deleteClearanceInfo(ids) {
  return request({
    url: '/zc-cggq-company-governance/delete',
    method: 'post',
    data: { ids: ids }
  })
}

// 通过id查询参股企业治理信息
export function getClearanceInfoById(id) {
  return request({
    url: `/zc-cggq-company-governance/get-by-id/${id}`,
    method: 'get'
  })
}
