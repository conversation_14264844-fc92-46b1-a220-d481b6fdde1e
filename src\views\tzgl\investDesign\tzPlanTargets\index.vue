<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanTargetsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzPlanTargetsTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

    <el-table
      ref="tzPlanTargetsTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanTargets"
      row-key="tptId"
      @sort-change="sortChange"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        fixed="right"
        show-overflow-tooltip
        width="100"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="50%"
      height="530px"
    >
      <tzPlanTargetsForm
        ref="tzPlanTargetsForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import { 
    tzPlanTargetsDoDeleteELog,
    tzPlanTargetsGetList,
    tzPlanTargetsDoSaveOrUpdLog,
  } from '@/api/tzgl/investDesign/tzPlanTargets'
  import DialogCard from 'common/DialogCard'
  import tzPlanTargetsSearch from './components/Search.vue'
  import tzPlanTargetsForm from './components/Form.vue'
  import tzPlanTargetsQuery from './components/Query.vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanTargets',
    components: {
      DialogCard,
      tzPlanTargetsSearch,
      tzPlanTargetsForm,
      tzPlanTargetsQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tptAmount: [
            { required: true, message: '请输入计划投资总金额', trigger: 'blur' }
          ],
          tptAmount0: [
            { required: true, message: '请输入第一年计划投资金额', trigger: 'blur' }
          ],
          tptAmount1: [
            { required: true, message: '请输入第二年计划投资金额', trigger: 'blur' }
          ],
          tptAmount2: [
            { required: true, message: '请输入第三年计划投资金额', trigger: 'blur' }
          ],
          tptAmount3: [
            { required: true, message: '请输入第四年计划投资金额', trigger: 'blur' }
          ],
          tptAmount4: [
            { required: true, message: '请输入第五年计划投资金额', trigger: 'blur' }
          ],
          tptAmount5: [
            { required: true, message: '请输入第六年计划投资金额', trigger: 'blur' }
          ],
          tptDirection: [
            { required: true, message: '请输入重点投资方向', trigger: 'blur' }
          ],
          tptOdId: [
            { required: false, message: '请输入单位ID', trigger: 'blur' }
          ],
          tptOeId: [
            { required: false, message: '请输入操作人员ID', trigger: 'blur' }
          ],
          tptOeName: [
            { required: false, message: '请输入操作人员', trigger: 'blur' }
          ],
          tptSort: [
            { required: true, message: '请输入投资分类', trigger: 'blur' }
          ],
          tptStatus: [
            { required: false, message: '请输入投资指标状态', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        isFullscreen: false,
        dialogVisible: false,
        height: this.$baseTableHeight(1, 1) - 30,
        checkList: ['单位','投资分类','重点投资方向','计划投资总金额','第一年计划投资金额','第二年计划投资金额','第三年计划投资金额','第四年计划投资金额','第五年计划投资金额','第六年计划投资金额'],
        columns: [
          { prop:'tptOdName', label:'单位', width:'300'},
          { prop:'tptSort', label:'投资分类', width:'200'},
          { prop:'tptDirection', label:'重点投资方向', width:'200'},
          { prop:'tptAmount', label:'计划投资总金额', width:'200'},
          { prop:'tptAmount0', label:'第一年计划投资金额', width:'200'},
          { prop:'tptAmount1', label:'第二年计划投资金额', width:'200'},
          { prop:'tptAmount2', label:'第三年计划投资金额', width:'200'},
          { prop:'tptAmount3', label:'第四年计划投资金额', width:'200'},
          { prop:'tptAmount4', label:'第五年计划投资金额', width:'200'},
          { prop:'tptAmount5', label:'第六年计划投资金额', width:'200'}
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          tptType:'投资',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      //列排序事件
      sortChange(sortColumn){
        this.searchForm.sortField = sortColumn.prop
        this.searchForm.sortOrder = sortColumn.order
        this.fetchData()
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanTargetsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await tzPlanTargetsDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      close() {
        this.$refs.tzPlanTargetsEdit.close()
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={tptType:'投资','tptStatus':'',tptOeName:this.loginUser.oeName,tptOeId:this.loginUser.oeId,tptOdId:this.loginUser.odId}
        this.editType = 'add'
        this.title = '添加'
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tptId) {
          this.$baseConfirm('确定删除投资计划吗', null, async () => {
            const msg = await tzPlanTargetsDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询关闭
      queryClose(){
        this.$refs.tzPlanTargetsQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.tzPlanTargetsQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await tzPlanTargetsGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
      // 关闭弹窗
      closeDialog() {
        this.dialogVisible = false
      }
    },
  }
</script>