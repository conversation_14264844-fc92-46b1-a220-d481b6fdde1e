<template>
  <div class="NoticeContainer">
    <div class="noticeListItem" v-for="(item,index) in dataList" @click="handleItemClick(item)" :key="index">
      <p v-if="index == 0" class="noticeIcon">置顶</p>
      <p class="noticeTitle">{{ item.docTitle }}</p>
      <p class="noticeTime">{{ item.docModifyTime }}</p>
    </div>
  </div>
</template>

<script>
  import { getNewList } from 'api/tzgl/project/workbench';

  export default {
    name: "Notice",
    props: {
      titleName: {
        type: String,
        default: '通知公告'
      }
    },
    data() {
      return {
        dataList:[]
      }
    },
    created(){
      this.getData();
    },
    methods: {
      async getData(){
        let params = {
          pageNo:1,
          pageSize: 10, 
          docCatId: 'tzgg',
        }
        if(this.titleName == '通知公告'){
          params.docCatId = 'tzgg'
        }else{
          params.docCatId = 'ggzd'
        }
        const {data:{records},code,msg} = await getNewList(params);
         if (code == 200) {
          this.dataList = records;
        } else {
          this.$message.error(msg);
        }
      },
      handleItemClick(row) {
        let pathHerf = this.$router.resolve({
          name: 'preview',
          query: {
            docId: row.docId,
          },
        })
        window.open(pathHerf.href, '_blank')
      }
    },
  }
</script>

<style lang="scss" scoped>
  .NoticeContainer {
    height: 248px;
    overflow-y: scroll;
  }
  .noticeListItem {
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px dashed #EBEBEB;
    padding: 16px 0;
    p {
      margin: 0px;
    }
    .noticeIcon {
      width: 40px;
      height: 26px;
      background-color: #CC1214;
      color: white;
      text-align: center;
      line-height: 26px;
      border-radius: 4px;
      margin-right: 16px;
    }
    .noticeTitle {
      width: 70%;
      font-size: 16px;
      line-height: 30px;
      color: #303133;
      white-space: nowrap; 
      overflow: hidden; 
      text-overflow: ellipsis;
    }
    .noticeTime {
      width: 30%;
      text-align: right;
      font-weight: 400;
      font-size: 16px;
      color: #909399;
      line-height: 40px;
    }
  }
  .noticeListItem:first-child {
    padding-top: 0;
  }
</style>