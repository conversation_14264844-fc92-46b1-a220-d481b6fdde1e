import request from '@/utils/request'

//列表分页加载数据
export function tzPlanListedTargetGetList(params) {
  return request({
    url: '/tz-plan-listed-target/vPageList',
    method: 'get',
    params,
  })
}
export function tzPlanListedTargetGetData(params) {
  return request({
    url: '/tz-plan-listed-target/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function tzPlanListedTargetDoSave(data) {
  return request({
    url: '/tz-plan-listed-target/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function tzPlanListedTargetDoUpdate(data) {
  return request({
    url: '/tz-plan-listed-target/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function tzPlanListedTargetDoSaveOrUpd(data) {
  return request({
    url: '/tz-plan-listed-target/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function tzPlanListedTargetDoSaveOrUpdLog(data) {
  return request({
    url: '/tz-plan-listed-target/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function tzPlanListedTargetDoDelete(data) {
  return request({
    url: '/tz-plan-listed-target/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function tzPlanListedTargetDoDeleteLog(data) {
  return request({
    url: '/tz-plan-listed-target/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function tzPlanListedTargetDoDeleteELog(data) {
  return request({
    url: '/tz-plan-listed-target/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function tzPlanListedTargetGetTreeList(params){
  return request({
    url: '/tz-plan-listed-target/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function tzPlanListedTargetDoExport(data) {
  return request({
    url: '/tz-plan-listed-target/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function tzPlanListedTargetGetStat(params) {
  return request({
    url: '/tz-plan-listed-target/vStat',
    method: 'get',
    params,
  })
}
