<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="80px"
        :model="form">
        <el-col :span="24">
          <el-form-item label="单位ID" prop="tpaOdId">
            <el-input v-model.trim="form.tpaOdId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作者ID" prop="tpaOeId">
            <el-input v-model.trim="form.tpaOeId" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作者" prop="tpaOeName">
            <el-input v-model.trim="form.tpaOeName" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资计划执行率" prop="tpaRate">
            <el-input-number v-model.trim="form.tpaRateStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpaRateEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="tpaStatus">
            <el-input v-model.trim="form.tpaStatus" clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资队伍建设(人数)" prop="tpaTeam">
            <el-input-number v-model.trim="form.tpaTeamStart" clearable></el-input-number> - <el-input-number v-model.trim="form.tpaTeamEnd" clearable></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="年份" prop="tpaYear">
            <el-input v-model.trim="form.tpaYear" clearable ></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanAbilityQuery',
    props: {
      form: {
        type: Object,
        require: true
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>