<template>
  <SmallCard title="项目查询">
    <el-radio-group v-model="radioValue" slot="leftTitleCont">
      <el-radio-button label="固定资产投资">固定资产投资</el-radio-button>
      <el-radio-button label="股权投融资">股权投融资</el-radio-button>
      <el-radio-button label="资产经营">资产经营</el-radio-button>
    </el-radio-group>

    <el-radio-group v-model="radioValue1"  slot="rightTitle">
      <el-radio-button label="推进中项目">推进中项目</el-radio-button>
      <el-radio-button label="已批复项目">已批复项目</el-radio-button>
    </el-radio-group>
     <VabTable 
      :tableHeader="tableHeader" 
      :tableData="tableData" 
      :tableHeight="tableHeight"
      :pageNo="tablePage.pageNo"
      :pageSize="tablePage.pageSize"
      :total="tablePage.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </SmallCard>
</template>

<script>
  import SmallCard from 'common/SmallCard';
  import VabTable from 'components/VabTable';
  import { getProjectList } from 'api/tzgl/project/workbench';

  export default {
    name: 'BoostProject',
    components: {
      SmallCard,
      VabTable
    },
    data() {
      return {
        radioValue:'固定资产投资',
        radioValue1:'推进中项目',
        tableHeader: [
          { type: 'index', width:'55', label: "序号",align:'center' }, 
          { prop: 'tppName', label:'项目名称',align:'center', width:'120' },
          { prop: 'tppCompanyName', label:'二级单位',align:'center', width:'120' },
          { prop: 'tppCurrentStage', label:'所处阶段',align:'center', width:'120' },
          { prop: 'tppStageProcessingTime', label:'阶段办理时间',align:'center', width:'150' },
          { prop: 'tppConsolidateStrengthen', label:'一巩固三做强类型',align:'center', width:'150' },
          { prop: 'tppCzCompanyName', label:'投资主体',align:'center', width:'120' },
          { prop: 'tppImportance', label:'项目分类',align:'center', width:'120' },
          { prop: 'tppTypeName', label:'项目类别',align:'center', width:'120' },
          { prop: 'tppProvince', label:'项目地点',align:'center', width:'120' },
          { prop: 'tppInvestmentPurpose', label:'投资目的',align:'center', width:'120' },
          { prop: 'tppBudgetThisYear', label:'投资/融资金额',align:'center', width:'150' },
          { prop: 'tppProjectContent', label:'项目内容',align:'center', width:'120' },
          { prop: 'tppIfDesignEvaluation', label:'是否涉及评估/估值',align:'center', width:'150' },
          { prop: 'tppValuationDate', label:'评估基准日',align:'center', width:'130' },
          { prop: 'tppAssessmentDue', label:'评估到期日',align:'center', width:'130' },
          { prop: 'tppEvaluationAgency', label:'评估机构',align:'center', width:'120' },
          { prop: 'tppEvaluationMethod', label:'评估方法',align:'center', width:'120' },
          { prop: 'tppFilingStatus', label:'备案情况',align:'center', width:'120' },
        ],
        tableData: [],
        tableHeight: this.$baseTableHeight(1, 1) / 2 - 32, 
        tablePage: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        }
      }
    },
    created(){
      this.getTableData();
    },
    watch:{
      radioValue(val){
        this.getTableData();
      },
      radioValue1(val){
        this.getTableData();
      }
    },
    methods:{
      async getTableData(){
        const params = {
          tppPlanType:this.radioValue,
          tppCurrentStage:this.radioValue1,
          ...this.tablePage,
        };
        const {data,code,msg} = await getProjectList(params);
        if (code == 200) {
          this.tableData = data.list;
          this.tablePage.total = data.total;
        } else {
          this.$message.error(msg);
        }
      },
      handleSizeChange(val) {
        this.tablePage.pageSize = val
      },
      handleCurrentChange(val) {
        this.tablePage.pageNo = val
      },
    },
  }
</script>

<style lang="scss" scoped>
</style>