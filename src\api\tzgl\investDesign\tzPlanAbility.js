import request from '@/utils/request'

//列表分页加载数据
export function tzPlanAbilityGetList(params) {
  return request({
    url: '/tz-plan-ability/vPageList',
    method: 'get',
    params,
  })
}
export function tzPlanAbilityGetData(params) {
  return request({
    url: '/tz-plan-ability/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function tzPlanAbilityDoSave(data) {
  return request({
    url: '/tz-plan-ability/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function tzPlanAbilityDoUpdate(data) {
  return request({
    url: '/tz-plan-ability/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function tzPlanAbilityDoSaveOrUpd(data) {
  return request({
    url: '/tz-plan-ability/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function tzPlanAbilityDoSaveOrUpdLog(data) {
  return request({
    url: '/tz-plan-ability/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function tzPlanAbilityDoDelete(data) {
  return request({
    url: '/tz-plan-ability/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function tzPlanAbilityDoDeleteLog(data) {
  return request({
    url: '/tz-plan-ability/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function tzPlanAbilityDoDeleteELog(data) {
  return request({
    url: '/tz-plan-ability/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function tzPlanAbilityGetTreeList(params){
  return request({
    url: '/tz-plan-ability/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function tzPlanAbilityDoExport(data) {
  return request({
    url: '/tz-plan-ability/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function tzPlanAbilityGetStat(params) {
  return request({
    url: '/tz-plan-ability/vStat',
    method: 'get',
    params,
  })
}
