<template>
  <div class="workbench">
    <!-- role为集团领导时展示推进中项目 -->
    <BoostProject v-if="role === '集团领导'"/>
    <!-- role为除集团领导的其他角色时，展示推进中的项目和流程数据详情 -->
    <header v-else>
      <BoostProject/>
      <el-tabs v-model="activeName" type="card" class="tabs">
        <el-tab-pane v-for="(item,index) in tabData" :key="index" :label="item.title" :name="index+''" class="tab-pane">
          <ProcessData v-if="index != (tabData.length - 1)" :activeName="activeName" :parentData="tabData[index]"/>
        </el-tab-pane>
      </el-tabs>
    </header>
    <!-- 通知公告&&规章制度 -->
    <footer>
      <SmallCard title="通知公告">
        <span class="detail-btn" slot="rightTitle" @click="()=>{}">
          查看更多
          <img :src="require(`@/assets/tzgl/icon_ckgd.png`)"/>
        </span>
        <Notice titleName="通知公告"/>
      </SmallCard>
      <SmallCard title="规章制度">
        <span class="detail-btn" slot="rightTitle" @click="()=>{}">
          查看更多
          <img :src="require(`@/assets/tzgl/icon_ckgd.png`)"/>
        </span>
        <Notice titleName="规章制度"/>
      </SmallCard>
    </footer>
  </div>
</template>

<script>
  import SmallCard from 'common/SmallCard';
  import BoostProject from './components/BoostProject';
  import ProcessData from './components/ProcessData';
  import Notice from './components/Notice';
  import { mapGetters } from 'vuex';

  export default {
    name: 'Workbench',
    components: {
      SmallCard,
      BoostProject,
      ProcessData,
      Notice
    },
    data() {
      return {
        role:'集团领导1',
        activeName: '0',
        tabData:[
          {
            title: '待处理',
          },
          {
            title: '已处理',
          },
          {
            title: '待阅',
          },
          {
            title: '已阅',
          }
        ]
      }
    },
    created(){

    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
    },
    method:{},
  }
</script>

<style lang="scss" scoped>
  .workbench {
    >header,>footer{
      display: flex;
      >div{
        width: calc(50% - 8px);
        margin: 0 8px;
      }
      >div:first-child{
        margin-left: 0;
      }
      >div:last-child{
        margin-right: 0;
      }
      .detail-btn{
        font-weight: 400;
        font-size: 14px;
        color: #606266;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    >footer{
      margin-top: 16px;
    }

    .tabs{
      background: white;
      .tab-pane{padding: 16px;}

      ::v-deep .el-tabs__nav-scroll{
        background: #f6f8f9;
      }
      ::v-deep .is-active,::v-deep .el-tab-pane{
        background: white;
      }
      ::v-deep .el-tabs__nav{
        height: 56px;
      }
    }

    
  }
</style>