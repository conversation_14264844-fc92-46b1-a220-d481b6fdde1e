<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="180px"
        :model="form"
        :rules="rules">
        <el-col :span="24">
          <el-form-item label="投资分类" prop="tptSort">
            <el-select v-model.trim="form.tptSort"   clearable   style="width:100%">
              <el-option v-for="item in optionsData.tptSort" :key="item.value" :label="item.label" :value="item.value"> </el-option> 
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="重点投资方向" prop="tptDirection">
            <el-input type="textarea" :rows="2" maxlength="1000" show-word-limit clearable v-model.trim="form.tptDirection"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="计划投资总金额" prop="tptAmount">
            <el-input-number v-model.trim="form.tptAmount" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第一年计划投资金额" prop="tptAmount0">
            <el-input-number v-model.trim="form.tptAmount0" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第二年计划投资金额" prop="tptAmount1">
            <el-input-number v-model.trim="form.tptAmount1" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第三年计划投资金额" prop="tptAmount2">
            <el-input-number v-model.trim="form.tptAmount2" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第四年计划投资金额" prop="tptAmount3">
            <el-input-number v-model.trim="form.tptAmount3" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第五年计划投资金额" prop="tptAmount4">
            <el-input-number v-model.trim="form.tptAmount4" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="第六年计划投资金额" prop="tptAmount5">
            <el-input-number v-model.trim="form.tptAmount5" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="单位ID" prop="tptOdId"  style="display:none;">
            <el-input v-model.trim="form.tptOdId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作人员ID" prop="tptOeId"  style="display:none;">
            <el-input v-model.trim="form.tptOeId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作人员" prop="tptOeName"  style="display:none;">
            <el-input v-model.trim="form.tptOeName" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主键" prop="tptId"  style="display:none;">
            <el-input v-model.trim="form.tptId" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投资指标状态" prop="tptStatus"  style="display:none;">
            <el-input v-model.trim="form.tptStatus" type="hidden"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投融资类型" prop="tptType"  style="display:none;">
            <el-input v-model.trim="form.tptType" type="hidden"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList,getSysValRedisList } from '@/api/lesysparamvals'
  export default {
    name: 'tzPlanTargetsForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          tptSort:[]
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const tptSort = await getSysValRedisList({ "lpvLpdId": "FINANCESORTS" });
        if(tptSort.code==200){
          this.getOptionsData(tptSort.data,"tptSort");
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvName,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>