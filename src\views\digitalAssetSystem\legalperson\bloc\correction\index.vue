<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handleIncludeNextYear">纳入下一年整改计划</el-button>
      </div>
      <el-button type="primary" icon="el-icon-search">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zccgId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <!-- 整改相关字段 -->
        <el-table-column prop="zccgSecondLevelUnit" label="二级成员单位" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgInvestmentEntity" label="投资主体" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgHoldingEnterpriseName" label="参股企业名称" width="160" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgCorrectionMethod" label="整改方式" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgEquityExitPlan" label="整改方案" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgCompletionTime" label="完成时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgLiquidationYear" label="整改年份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgPlanningStatus" label="分析建议" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgSecondLevelUnitSuggestion" label="整改建议" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsCompliantExit" label="是否完成整改" width="120" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.zccgIsCompliantExit || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleViewProgress(scope.row)">查看进展</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 进展情况弹窗 -->
    <ProgressDialog :visible.sync="progressDialogVisible" :readonly="true" :progress-records="currentProgressRecords" @view-attachment="handleViewAttachment" />


  </div>
</template>

<script>
import { getCorrectionList } from '@/api/digitalAssetSystem/treatment/correction'
import ProgressDialog from './components/progressDialog.vue'

export default {
  components: {
    ProgressDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 进展情况弹窗相关
      progressDialogVisible: false,
      currentProgressRecords: [],
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getCorrectionList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleDetail (row) {
      console.log('查看详情:', row)
      // 可以在这里打开详情弹窗或跳转到详情页面
    },



    // 纳入下一年整改计划
    handleIncludeNextYear () {
      this.$message.info('纳入下一年整改计划功能')
      // 这里可以实现具体的业务逻辑
    },

    // 查看进展情况
    handleViewProgress (row) {
      this.currentRowData = row
      // 根据实际业务逻辑设置进展记录数据
      this.currentProgressRecords = [
        {
          date: '2025-07-11',
          isCompleted: '是',
          progress: '已完成初步整改方案制定，相关文件已提交审核。整改工作按计划推进中，预计下月完成全部整改任务。',
          attachments: [
            { name: '整改方案文件.pdf', url: '#' },
            { name: '进度报告.docx', url: '#' },
            { name: '相关证明材料.jpg', url: '#' }
          ]
        },
        {
          date: '2025-08-11',
          isCompleted: '部分完成',
          progress: '整改工作进展顺利，已完成70%的整改任务。剩余工作主要集中在设备更新和流程优化方面，预计本月底前全部完成。',
          attachments: [
            { name: '阶段性报告.pdf', url: '#' },
            { name: '设备更新清单.xlsx', url: '#' },
            { name: '现场照片.jpg', url: '#' }
          ]
        },
        {
          date: '2025-08-21',
          isCompleted: '否',
          progress: '本月计划完成剩余整改工作，目前正在进行最后阶段的设备调试和系统测试。',
          attachments: [
            { name: '测试报告.pdf', url: '#' },
            { name: '调试记录.docx', url: '#' }
          ]
        }
      ]
      this.progressDialogVisible = true
    },

    // 查看附件
    handleViewAttachment (file) {
      this.$message.info(`查看文件：${file.name}`)
      // 这里可以实现文件预览或下载功能
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}


</style>
