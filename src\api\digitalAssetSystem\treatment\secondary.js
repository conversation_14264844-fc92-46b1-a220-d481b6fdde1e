import request from '@/utils/request'

// 分页查询参股企业治理信息表（二级单位相关）
export function getSecondaryUnitList(query) {
  return request({
    url: '/zc-cggq-company-governance/vPageList',
    method: 'get',
    params: query
  })
}

// 新增参股企业治理信息（二级单位）
export function addSecondaryUnitInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/save',
    method: 'post',
    data: data
  })
}

// 修改参股企业治理信息（二级单位）
export function updateSecondaryUnitInfo(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 删除参股企业治理信息（二级单位）
export function deleteSecondaryUnitInfo(ids) {
  return request({
    url: '/zc-cggq-company-governance/delete',
    method: 'post',
    data: { ids: ids }
  })
}

// 获取单条记录详情
export function getSecondaryUnitDetail(id) {
  return request({
    url: `/zc-cggq-company-governance/get-by-id/${id}`,
    method: 'get'
  })
}

// 批量更新二级单位状态
export function batchUpdateSecondaryUnitStatus(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 导出二级单位数据
export function exportSecondaryUnitData(query) {
  return request({
    url: '/zc-cggq-company-governance/vExport',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 审核计划
export function reviewPlan(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 进展填报
export function reportProgress(data) {
  return request({
    url: '/zc-cggq-company-governance/update',
    method: 'post',
    data: data
  })
}

// 获取进展填报列表
export function getProgressList(params) {
  return request({
    url: '/zc-cggq-progress/vList',
    method: 'get',
    params
  })
}

// 新增进展填报
export function saveProgress(data) {
  return request({
    url: '/zc-cggq-progress/save',
    method: 'post',
    data
  })
}

// 修改进展填报
export function updateProgress(data) {
  return request({
    url: '/zc-cggq-progress/update',
    method: 'post',
    data
  })
}

// 新增或修改进展填报(带日志)
export function saveOrUpdateProgress(data) {
  return request({
    url: '/zc-cggq-progress/saveOrUpdLog',
    method: 'post',
    data
  })
}

// 根据ID获取进展填报详情
export function getProgressById(id) {
  return request({
    url: `/zc-cggq-progress/get-by-id/${id}`,
    method: 'get'
  })
}
