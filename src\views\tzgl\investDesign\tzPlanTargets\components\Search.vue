<template>
  <el-form
    ref="form"
    :inline="true"
    label-width="100"
    :model="queryForm"
    @submit.native.prevent
  >
    <el-form-item>
      <el-input placeholder="计划投资总金额" v-model="tableQueryForm.tptAmount"  @keyup.enter.native="handleSearch" />
    </el-form-item>
    <el-form-item label="单位名称：">
      <el-input placeholder="计划投资总金额" v-model="tableQueryForm.tptOdName"  @keyup.enter.native="handleSearch" />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSearch">
        查询
      </el-button>
    </el-form-item>
    <el-form-item>
      <el-button @click="handleAdd">
        添加
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    name: 'TzPlanTargetsSearch',
    props: {
      checkList: {
        type:Array
      },
      columns: {
        type:Array
      },
      queryForm: {
        type:Object
      }
    },
    data() {
      return {
        isFullscreen: false,
        tableQueryForm:this.queryForm,
        tableColums: this.columns,
        taleCheckList: this.checkList,
      }
    },
    watch: {
      taleCheckList(newVal) {
        this.taleCheckList = newVal
      }
    },
    methods: {
      // 监听查询按钮点击事件
      handleSearch() {
        this.$emit('handleSearch',this.tableQueryForm)
      },
      // 监听添加按钮点击事件
      handleAdd() {
        this.$emit('handleAdd')
      },
    },
  }
</script>