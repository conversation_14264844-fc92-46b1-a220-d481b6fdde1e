<template>
  <div class="category-table-container">
    <!-- 操作工具栏 -->
    <el-card class="operation-bar" shadow="hover">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索附件名称或分类"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          ></el-input>
        </el-col>
        <el-col :span="16" class="text-right">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            新增附件
          </el-button>
          <el-button type="warning" icon="el-icon-edit" @click="handleEdit" :disabled="!selectedRow">
            编辑
          </el-button>
          <el-button type="danger" icon="el-icon-delete" @click="handleDelete" :disabled="!selectedRow">
            删除
          </el-button>
          <el-button 
            type="info" 
            icon="el-icon-refresh" 
            @click="handleStatusChange" 
            :disabled="!selectedRow"
          >
            {{ selectedRow ? (selectedRow.status === '启用' ? '禁用' : '启用') : '状态切换' }}
          </el-button>
          <el-button 
            type="success" 
            icon="el-icon-sort" 
            @click="toggleAllPanels"
          >
            {{ expandAll ? '全部折叠' : '全部展开' }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 折叠面板内容 -->
    <div class="table-content" v-loading="loading" element-loading-text="加载中...">
      <!-- 无数据提示 -->
      <div v-if="filteredGroups.length === 0 && !loading" class="no-data">
        <el-empty description="暂无数据"></el-empty>
      </div>
      
      <!-- 分类折叠面板 -->
      <el-collapse 
        v-else
        v-model="activeNames" 
        :accordion="false"
        class="category-collapse"
      >
        <el-collapse-item 
          v-for="(group, groupIndex) in filteredGroups" 
          :key="groupIndex" 
          :title="getPanelTitle(group)"
          :name="group.category"
        >
          <div slot="title" class="panel-title">
            <i class="el-icon-folder-opened"></i>
            <span>{{ group.category }}</span>
            <span class="count-badge">{{ group.children.length }} 项</span>
            <el-button 
              type="text" 
              size="mini" 
              @click.stop="handleAddToCategory(group.category)"
              class="add-to-category-btn"
            >
              <i class="el-icon-plus"></i> 添加
            </el-button>
          </div>
          
          <!-- 分类下的表格 -->
          <el-table
            :data="group.children"
            border
            style="width: 100%"
            @row-click="handleRowClick"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#f5f7fa' }"
          >
            <el-table-column
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="60"
              :index="(index) => getIndex(groupIndex, index)"
            ></el-table-column>
            <el-table-column
              prop="name"
              label="附件名称"
              min-width="180"
            ></el-table-column>
            <el-table-column
              prop="isShared"
              label="是否共用附件"
              min-width="130"
            >
              <template slot-scope="scope">
                <el-tag :type="scope.row.isShared === '是' ? 'success' : 'info'">
                  {{ scope.row.isShared || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              min-width="200"
            ></el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              min-width="100"
            >
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-text="启用"
                  inactive-text="禁用"
                  :active-value="'启用'"
                  :inactive-value="'禁用'"
                  @change="handleStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleEdit(scope.row)"
                >编辑</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  style="color: #F56C6C;"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        background
        layout="prev, pager, next, sizes, total"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="120px"
      >
        <el-form-item label="附件分类" prop="category">
          <el-select
            v-model="formData.category"
            placeholder="请选择附件分类"
            clearable
          >
            <el-option
              v-for="category in allCategories"
              :key="category"
              :label="category"
              :value="category"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="附件名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入附件名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="是否共用附件" prop="isShared">
          <el-radio-group v-model="formData.isShared">
            <el-radio label="是"></el-radio>
            <el-radio label="否"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            placeholder="请输入备注信息"
            type="textarea"
            rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio label="启用"></el-radio>
            <el-radio label="禁用"></el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 确认删除对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="300px"
      :show-close="false"
    >
      <p>确定要删除该附件吗？此操作不可撤销。</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CollapseCategoryTable',
  data() {
    return {
      // 表格数据 - 按分类分组
      categoryGroups: [],
      filteredGroups: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      loading: false,
      
      // 搜索相关
      searchKeyword: '',
      
      // 折叠面板状态
      activeNames: [], // 存储当前展开的面板名称
      expandAll: false,
      
      // 选中状态
      selectedRow: null,
      multipleSelection: [],
      
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      formData: {
        id: null,
        name: '',
        category: '',
        isShared: '否',
        remark: '',
        status: '启用'
      },
      formRules: {
        name: [
          { required: true, message: '请输入附件名称', trigger: 'blur' },
          { min: 1, max: 50, message: '附件名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择附件分类', trigger: 'change' }
        ]
      },
      
      // 删除对话框
      deleteDialogVisible: false,
      currentDeleteRow: null,
      
      // 所有分类
      allCategories: []
    };
  },
  created() {
    // 初始化加载数据
    this.loadData();
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 模拟API请求
        await this.mockApiRequest();
        
        // 提取所有分类
        this.extractAllCategories();
        
        // 初始化时展开所有面板
        this.activeNames = this.categoryGroups.map(group => group.category);
        this.expandAll = true;
        
        // 处理过滤
        this.handleSearch();
      } catch (error) {
        this.$message.error('加载数据失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },
    
    // 模拟API请求
    mockApiRequest() {
      return new Promise(resolve => {
        setTimeout(() => {
          // 模拟原始数据
          const rawData = [
            { id: 1, name: '安全设施验收报告', category: '三同时附件', isShared: '否', remark: '项目验收必备', status: '启用' },
            { id: 2, name: '环保评估报告', category: '三同时附件', isShared: '否', remark: '', status: '启用' },
            { id: 3, name: '职业卫生评价', category: '三同时附件', isShared: '是', remark: '多个项目共用', status: '启用' },
            { id: 4, name: '付款凭证', category: '产权交割附件', isShared: '', remark: '', status: '启用' },
            { id: 5, name: '不动产证复印件', category: '产权交割附件', isShared: '否', remark: '原件存放于档案室', status: '启用' },
            { id: 6, name: '产权转移协议', category: '产权交割附件', isShared: '否', remark: '', status: '禁用' },
            { id: 7, name: '项目可行性研究', category: '立项附件', isShared: '是', remark: '', status: '启用' },
            { id: 8, name: '立项批复文件', category: '立项附件', isShared: '否', remark: '', status: '启用' },
            { id: 9, name: '规划许可证', category: '规划附件', isShared: '否', remark: '', status: '启用' },
            { id: 10, name: '规划红线图', category: '规划附件', isShared: '否', remark: '', status: '启用' },
            { id: 11, name: '消防设计审查', category: '消防附件', isShared: '否', remark: '', status: '启用' },
            { id: 12, name: '消防验收报告', category: '消防附件', isShared: '否', remark: '', status: '禁用' }
          ];
          
          // 对数据进行分组处理
          this.categoryGroups = this.groupDataByCategory(rawData);
          this.total = rawData.length;
          resolve();
        }, 800);
      });
    },
    
    // 按分类分组数据
    groupDataByCategory(rawData) {
      const grouped = {};
      
      // 先按分类分组
      rawData.forEach(item => {
        if (!grouped[item.category]) {
          grouped[item.category] = {
            category: item.category,
            children: []
          };
        }
        grouped[item.category].children.push(item);
      });
      
      // 转换为数组并排序
      return Object.values(grouped).sort((a, b) => a.category.localeCompare(b.category));
    },
    
    // 获取面板标题
    getPanelTitle(group) {
      return `${group.category} (${group.children.length}项)`;
    },
    
    // 提取所有分类
    extractAllCategories() {
      const categories = new Set();
      this.categoryGroups.forEach(group => {
        categories.add(group.category);
      });
      this.allCategories = Array.from(categories).sort();
    },
    
    // 搜索处理
    handleSearch() {
      if (!this.searchKeyword) {
        // 如果没有搜索关键词，直接使用原始数据
        this.filteredGroups = [...this.categoryGroups];
      } else {
        const keyword = this.searchKeyword.toLowerCase();
        // 过滤包含关键词的分组和项目
        this.filteredGroups = this.categoryGroups.reduce((result, group) => {
          // 检查分组名称是否匹配
          const groupMatches = group.category.toLowerCase().includes(keyword);
          
          // 过滤组内匹配的项目
          const filteredChildren = group.children.filter(item => 
            item.name.toLowerCase().includes(keyword) || 
            item.category.toLowerCase().includes(keyword)
          );
          
          // 如果组名匹配或者有匹配的子项，保留该组
          if (groupMatches || filteredChildren.length > 0) {
            result.push({
              ...group,
              children: filteredChildren
            });
          }
          
          return result;
        }, []);
      }
    },
    
    // 获取序号
    getIndex(groupIndex, itemIndex) {
      // 计算全局序号
      let index = 0;
      // 累加前面所有组的数量
      for (let i = 0; i < groupIndex; i++) {
        index += this.filteredGroups[i].children.length;
      }
      // 加上当前组内的索引
      return index + itemIndex + 1;
    },
    
    // 处理行点击
    handleRowClick(row) {
      this.selectedRow = row;
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
      this.selectedRow = selection.length > 0 ? selection[0] : null;
    },
    
    // 切换所有面板的展开/折叠状态
    toggleAllPanels() {
      this.expandAll = !this.expandAll;
      if (this.expandAll) {
        // 展开所有
        this.activeNames = this.filteredGroups.map(group => group.category);
      } else {
        // 折叠所有
        this.activeNames = [];
      }
    },
    
    // 处理分页大小变化
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },
    
    // 处理页码变化
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    
    // 新增附件
    handleAdd() {
      this.dialogTitle = '新增附件';
      this.formData = {
        id: null,
        name: '',
        category: '',
        isShared: '否',
        remark: '',
        status: '启用'
      };
      this.dialogVisible = true;
    },
    
    // 向指定分类添加附件
    handleAddToCategory(category) {
      this.dialogTitle = '新增附件';
      this.formData = {
        id: null,
        name: '',
        category: category,
        isShared: '否',
        remark: '',
        status: '启用'
      };
      this.dialogVisible = true;
    },
    
    // 编辑附件
    handleEdit(row) {
      if (!row && !this.selectedRow) return;
      
      const editRow = row || this.selectedRow;
      this.dialogTitle = '编辑附件';
      this.formData = { ...editRow };
      this.dialogVisible = true;
    },
    
    // 关闭对话框
    handleDialogClose() {
      this.$refs.formRef.resetFields();
      this.dialogVisible = false;
    },
    
    // 对话框确认
    handleDialogConfirm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            
            // 模拟API请求
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (this.formData.id) {
              // 编辑操作
              this.updateRowData(this.formData);
              this.$message.success('附件更新成功');
            } else {
              // 新增操作
              const newRow = {
                ...this.formData,
                id: Date.now() // 模拟生成ID
              };
              this.addRowData(newRow);
              this.$message.success('附件新增成功');
            }
            
            // 刷新分类列表
            this.extractAllCategories();
            
            // 关闭对话框
            this.dialogVisible = false;
          } catch (error) {
            this.$message.error('操作失败: ' + error.message);
          } finally {
            this.loading = false;
          }
        }
      });
    },
    
    // 添加新行数据
    addRowData(newRow) {
      let categoryExists = false;
      
      // 检查分类是否已存在
      for (let i = 0; i < this.categoryGroups.length; i++) {
        if (this.categoryGroups[i].category === newRow.category) {
          this.categoryGroups[i].children.push(newRow);
          categoryExists = true;
          break;
        }
      }
      
      // 如果分类不存在，创建新分类
      if (!categoryExists) {
        const newGroup = {
          category: newRow.category,
          children: [newRow]
        };
        this.categoryGroups.push(newGroup);
        // 重新排序
        this.categoryGroups.sort((a, b) => a.category.localeCompare(b.category));
        
        // 如果设置了全部展开，新分类也需要展开
        if (this.expandAll) {
          this.activeNames.push(newGroup.category);
        }
      }
      
      // 更新过滤后的数据
      this.handleSearch();
      this.total++;
    },
    
    // 更新行数据
    updateRowData(updatedRow) {
      // 检查是否需要变更分类
      let originalCategory = null;
      let originalGroupIndex = -1;
      let originalItemIndex = -1;
      
      // 查找原始数据位置
      for (let i = 0; i < this.categoryGroups.length; i++) {
        const group = this.categoryGroups[i];
        for (let j = 0; j < group.children.length; j++) {
          if (group.children[j].id === updatedRow.id) {
            originalCategory = group.category;
            originalGroupIndex = i;
            originalItemIndex = j;
            break;
          }
        }
        if (originalCategory) break;
      }
      
      if (originalCategory === updatedRow.category) {
        // 如果分类没变，直接更新
        this.categoryGroups[originalGroupIndex].children[originalItemIndex] = updatedRow;
      } else {
        // 如果分类变了，先删除原数据
        this.categoryGroups[originalGroupIndex].children.splice(originalItemIndex, 1);
        // 如果分组为空，删除分组
        if (this.categoryGroups[originalGroupIndex].children.length === 0) {
          // 如果删除的分组是展开状态，从activeNames中移除
          const index = this.activeNames.indexOf(originalCategory);
          if (index !== -1) {
            this.activeNames.splice(index, 1);
          }
          this.categoryGroups.splice(originalGroupIndex, 1);
        }
        // 添加到新分类
        this.addRowData(updatedRow);
        return;
      }
      
      // 更新过滤后的数据
      this.handleSearch();
    },
    
    // 处理删除
    handleDelete(row) {
      if (!row && !this.selectedRow) return;
      
      this.currentDeleteRow = row || this.selectedRow;
      this.deleteDialogVisible = true;
    },
    
    // 确认删除
    confirmDelete() {
      if (!this.currentDeleteRow) return;
      
      try {
        this.loading = true;
        
        // 模拟API请求
        setTimeout(() => {
          const rowId = this.currentDeleteRow.id;
          const rowCategory = this.currentDeleteRow.category;
          
          // 查找并删除数据
          for (let i = 0; i < this.categoryGroups.length; i++) {
            const group = this.categoryGroups[i];
            for (let j = 0; j < group.children.length; j++) {
              if (group.children[j].id === rowId) {
                group.children.splice(j, 1);
                // 如果分组为空，删除分组
                if (group.children.length === 0) {
                  // 如果删除的分组是展开状态，从activeNames中移除
                  const index = this.activeNames.indexOf(rowCategory);
                  if (index !== -1) {
                    this.activeNames.splice(index, 1);
                  }
                  this.categoryGroups.splice(i, 1);
                }
                // 刷新分类列表
                this.extractAllCategories();
                // 更新过滤后的数据
                this.handleSearch();
                this.total--;
                this.$message.success('附件已删除');
                this.deleteDialogVisible = false;
                this.selectedRow = null;
                this.currentDeleteRow = null;
                return;
              }
            }
          }
        }, 500);
      } catch (error) {
        this.$message.error('删除失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },
    
    // 切换状态
    handleStatusChange(row) {
      // 确保操作对象存在
      const targetRow = row || this.selectedRow;
      if (!targetRow) return;
      
      const newStatus = targetRow.status === '启用' ? '禁用' : '启用';
      
      // 模拟API请求
      this.loading = true;
      setTimeout(() => {
        try {
          // 更新状态
          targetRow.status = newStatus;
          this.$message.success(`附件已${newStatus}`);
        } catch (error) {
          this.$message.error('状态更新失败: ' + error.message);
        } finally {
          this.loading = false;
        }
      }, 300);
    }
  }
};
</script>

<style scoped>
.category-table-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.category-collapse {
  margin-top: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

::v-deep .el-collapse-item__header{
  padding-bottom: 16px;
}

::v-deep .el-collapse-item {
  padding: 16px 16px 0 16px;
}

::v-deep .el-collapse-item:last-child {
  border-bottom: none;
}

.panel-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  width: 100%;
}

.panel-title i {
  margin-right: 8px;
  color: #409EFF;
}

.count-badge {
  margin-left: 10px;
  font-size: 12px;
  color: #888;
  background-color: #f0f2f5;
  padding: 2px 8px;
  border-radius: 12px;
}

.add-to-category-btn {
  margin-left: auto;
  color: #409EFF;
  padding: 5px 10px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.no-data {
  margin: 50px 0;
  text-align: center;
}

/* 调整操作列按钮间距 */
::v-deep .el-table .el-button--text {
  margin-right: 10px;
}

::v-deep .el-table .el-button--text:last-child {
  margin-right: 0;
}
</style>
