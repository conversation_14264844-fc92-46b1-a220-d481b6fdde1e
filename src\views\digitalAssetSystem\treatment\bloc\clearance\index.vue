<template>
  <div class="custom-table-container">
    <div class="search-container">
      <div>
        <el-button type="primary" @click="handleRectificationPlan">推送年度参股企业清退计划</el-button>
        <el-button type="primary">上报国资委</el-button>
        <el-button type="primary">纳入下一清退改计划</el-button>
      </div>
      <el-button type="primary" icon="el-icon-search">综合查询</el-button>
    </div>
    <div class="table-section">
      <el-table :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zccgId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zccgSecondLevelUnit" label="二级成员单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgInvestmentEntity" label="投资主体" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgHoldingEnterpriseName" label="参股企业名称" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgEquityExitPlan" label="清退方式" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgGroupInternalInvestmentAmount" label="股权退出行为预计对集团公司合并报表产生的非经常性损益金额（万元）" width="280" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgDisposalOpinion" label="处置收益是否纳入本年度财务预算" width="200" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgNoPlanReason" label="若计划清退，但不能在当年完成，请简述原因（100字以内）" width="250" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgLiquidationYear" label="清退年份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zccgIsCompliantExit" label="是否完成清退" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="text" size="small" @click="handleProgress(scope.row)">进展情况</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 推送年度参股企业清退计划弹窗 -->
    <RectificationPlan :visible.sync="rectificationPlanVisible" :initial-data="rectificationPlanData" @confirm="handleRectificationPlanConfirm" />

    <!-- 进展情况弹窗 -->
    <ProgressDialog :visible.sync="progressDialogVisible" :current-step="currentProgressStep" :progress-data="currentProgressData" @view-plan="handleViewPlan" @view-attachment="handleViewAttachment" />
  </div>
</template>

<script>
import { getClearanceList } from '@/api/digitalAssetSystem/treatment/clearance.js'
import RectificationPlan from './components/rectificationPlan.vue'
import ProgressDialog from './components/progressDialog.vue'

export default {
  components: {
    RectificationPlan,
    ProgressDialog
  },
  data () {
    return {
      loading: false,
      height: this.$baseTableHeight(1, 1),
      searchForm: {
        pageNo: 1,
        pageSize: 20
      },
      tableData: [],
      total: 0,
      // 推送年度参股企业清退计划弹窗相关
      rectificationPlanVisible: false,
      rectificationPlanData: {},
      // 进展情况弹窗相关
      progressDialogVisible: false,
      currentProgressStep: 1,
      currentProgressData: {},
      currentRowData: null
    }
  },

  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getClearanceList(this.searchForm).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          // 如果接口暂时没有数据，使用模拟数据展示表格效果
          this.tableData = this.getMockData()
          this.total = this.tableData.length
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        // 接口调用失败时使用模拟数据
        this.tableData = this.getMockData()
        this.total = this.tableData.length
        console.warn('接口调用失败，使用模拟数据')
      })
    },

    // 模拟数据方法
    getMockData () {
      return [
        {
          zccgId: '1',
          zccgSecondLevelUnit: 'xxx',
          zccgInvestmentEntity: 'xxx',
          zccgHoldingEnterpriseName: 'xxx',
          zccgEquityExitPlan: '是',
          zccgGroupInternalInvestmentAmount: '是',
          zccgDisposalOpinion: '是',
          zccgNoPlanReason: '是',
          zccgLiquidationYear: '2025',
          zccgIsCompliantExit: '是'
        },
        {
          zccgId: '2',
          zccgSecondLevelUnit: '',
          zccgInvestmentEntity: '',
          zccgHoldingEnterpriseName: '',
          zccgEquityExitPlan: '',
          zccgGroupInternalInvestmentAmount: '',
          zccgDisposalOpinion: '',
          zccgNoPlanReason: '',
          zccgLiquidationYear: '',
          zccgIsCompliantExit: ''
        },
        {
          zccgId: '3',
          zccgSecondLevelUnit: '',
          zccgInvestmentEntity: '',
          zccgHoldingEnterpriseName: '',
          zccgEquityExitPlan: '',
          zccgGroupInternalInvestmentAmount: '',
          zccgDisposalOpinion: '',
          zccgNoPlanReason: '',
          zccgLiquidationYear: '',
          zccgIsCompliantExit: ''
        }
      ]
    },

    handleDetail (row) {
      console.log('查看详情:', row)
    },

    // 推送年度参股企业清退计划
    handleRectificationPlan () {
      this.rectificationPlanData = {}
      this.rectificationPlanVisible = true
    },

    // 推送年度参股企业清退计划确认
    handleRectificationPlanConfirm (formData) {
      console.log('推送年度参股企业清退计划提交:', formData)
      // 这里可以调用API提交数据
      this.$message.success('推送成功')
    },

    // 查看进展情况
    handleProgress (row) {
      this.currentRowData = row
      // 根据实际业务逻辑设置当前步骤和进展数据
      this.currentProgressStep = 2 // 示例：当前在第2步
      this.currentProgressData = {
        step1: {
          handler: '张三',
          department: '资产管理部',
          info: '已完成年度参股企业清退计划的制定和上报工作'
        },
        step2: {
          departmentOpinions: [
            '计划内容详实，建议通过-2025-08-21，部门领导',
            '需要补充风险评估内容-2025-08-22，部门领导',
            '修改后同意通过-2025-08-23，部门领导'
          ],
          leaderOpinions: [
            '总体方案可行-2025-08-21，总经理',
            '注意控制风险-2025-08-22，总经理',
            '同意执行-2025-08-23，总经理'
          ]
        },
        step3: {
          departmentOpinions: [
            '按计划执行清退工作-2025-08-24，部门领导'
          ],
          leaderOpinions: [
            '严格按照计划执行-2025-08-24，总经理'
          ]
        },
        step4: {
          work: '产权登记变更、工商变更登记',
          progress: '正在办理中'
        }
      }
      this.progressDialogVisible = true
    },

    // 查看计划文件
    handleViewPlan () {
      this.$message.info('查看年度参股企业整改计划文件')
      // 这里可以实现文件预览或下载功能
    },

    // 查看附件
    handleViewAttachment (type) {
      const typeMap = {
        property: '产权登记附件',
        business: '工商变更附件',
        contract: '合同附件'
      }
      this.$message.info(`查看${typeMap[type]}`)
      // 这里可以实现附件预览或下载功能
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>


<style lang="scss" scoped>
.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
