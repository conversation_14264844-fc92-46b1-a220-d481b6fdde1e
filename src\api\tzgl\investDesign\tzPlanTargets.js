import request from '@/utils/request'

//列表分页加载数据
export function tzPlanTargetsGetList(params) {
  return request({
    url: '/tz-plan-targets/vPageList',
    method: 'get',
    params,
  })
}
export function tzPlanTargetsGetData(params) {
  return request({
    url: '/tz-plan-targets/vList',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function tzPlanTargetsDoSave(data) {
  return request({
    url: '/tz-plan-targets/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function tzPlanTargetsDoUpdate(data) {
  return request({
    url: '/tz-plan-targets/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function tzPlanTargetsDoSaveOrUpd(data) {
  return request({
    url: '/tz-plan-targets/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function tzPlanTargetsDoSaveOrUpdLog(data) {
  return request({
    url: '/tz-plan-targets/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function tzPlanTargetsDoDelete(data) {
  return request({
    url: '/tz-plan-targets/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function tzPlanTargetsDoDeleteLog(data) {
  return request({
    url: '/tz-plan-targets/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function tzPlanTargetsDoDeleteELog(data) {
  return request({
    url: '/tz-plan-targets/deleteLog',
    method: 'post',
    data,
  })
}
//后端导出
export function tzPlanTargetsDoExport(data) {
  return request({
    url: '/tz-plan-targets/vExport',
    method: 'post',
    data,
  })
}
