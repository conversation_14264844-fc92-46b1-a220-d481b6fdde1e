<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanListedTargetSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzPlanListedTargetTs"
      @handleAdd="handleAdd"
      @handleHeight="handleHeight"
      @handleSearch="handleSearch"
      @handleCheckedChange="handleCheckedChange" />

    <el-table
      ref="tzPlanListedTargetTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanListedTarget"
      row-key="tplt"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="100"
        fixed="right"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="50%"
      height="530px"
    >
      <tzPlanListedTargetForm
        ref="tzPlanListedTargetForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>

  </div>
</template>

<script>
  import { 
    tzPlanListedTargetDoDeleteELog,
    tzPlanListedTargetGetList,
    tzPlanListedTargetDoSaveOrUpdLog
  } from '@/api/tzgl/investDesign/tzPlanListedTarget'
  import DialogCard from 'common/DialogCard'
  import tzPlanListedTargetSearch from './components/Search.vue'
  import tzPlanListedTargetForm from './components/Form.vue'
  import tzPlanListedTargetQuery from './components/Query.vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanListedTarget',
    components: {
      DialogCard,
      tzPlanListedTargetSearch,
      tzPlanListedTargetForm,
      tzPlanListedTargetQuery
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tpltDividendRatio: [
            { required: true, message: '请输入现金分红比率', trigger: 'blur' }
          ],
          tpltMarketValue: [
            { required: true, message: '请输入上市公司市值(亿元)', trigger: 'blur' }
          ],
          tpltPriceEps: [
            { required: true, message: '请输入上市公司基本每股收益(EPS)年增长率', trigger: 'blur' }
          ],
          tpltPriceRank: [
            { required: true, message: '请输入市值排名(申万一级行业或恒生综合行业前%)', trigger: 'blur' }
          ],
          tpltPriceRate: [
            { required: true, message: '请输入股价相对综合增长率', trigger: 'blur' }
          ],
          tpltPriceRatio: [
            { required: true, message: '请输入上市公司市净率(是否破净)', trigger: 'blur' }
          ],
          tpltProportion: [
            { required: true, message: '请输入机构投资者占比', trigger: 'blur' }
          ],
          tpltQualityRating: [
            { required: true, message: '请输入证券交易所信息披露质量等级', trigger: 'blur' }
          ],
          // tpltStatus: [
          //   { required: true, message: '请输入状态', trigger: 'blur' }
          // ],
          tpltYear: [
            { required: true, message: '请输入年度', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: this.$baseTableHeight(1, 1) - 30,
        checkList: ['单位','现金分红比率','上市公司市值(亿元)','上市公司基本每股收益(EPS)年增长率','市值排名(申万一级行业或恒生综合行业前%)','股价相对综合增长率','上市公司市净率(是否破净)','机构投资者占比','证券交易所信息披露质量等级','状态','年度'],
        columns: [
          { prop:'tpltOdName', label:'单位', width:'300'},
          // { prop:'tpltStatus', label:'状态', width:'150'},
          { prop:'tpltYear', label:'年度', width:'150'},
          { prop:'tpltDividendRatio', label:'现金分红比率', width:'150'   },
          { prop:'tpltMarketValue', label:'上市公司市值(亿元)', width:'150'},
          { prop:'tpltPriceEps', label:'上市公司基本每股收益(EPS)年增长率', width:'200'},
          { prop:'tpltPriceRank', label:'市值排名(申万一级行业或恒生综合行业前%)', width:'200'},
          { prop:'tpltPriceRate', label:'股价相对综合增长率', width:'150'},
          { prop:'tpltPriceRatio', label:'上市公司市净率(是否破净)', width:'200'},
          { prop:'tpltProportion', label:'机构投资者占比', width:'150'},
          { prop:'tpltQualityRating', label:'证券交易所信息披露质量等级', width:'220'},
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanListedTargetForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const  msg  = await tzPlanListedTargetDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      // 可拖拽列复选框点击事件
      handleCheckedChange($event) {
        this.checkList = $event
      },
      // 全屏事件
      handleHeight($event) {
        this.isFullscreen = $event
        if ($event) {
          this.height = this.$baseTableHeight(1,1) + 150
        }else{
          this.height = this.$baseTableHeight(1,1)
        }
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={tpltOdId:this.loginUser.odId,tpltOeId:this.loginUser.oeId,tpltOeName:this.loginUser.oeName,tpltStatus:''}
        this.editType = 'add'
        this.title = '添加'
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tpltId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzPlanListedTargetDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      //高级查询关闭
      queryClose(){
        this.$refs.tzPlanListedTargetQuerySearch.close()
      },
      //高级查询清空
      queryClear(){
        this.queryForm = Object.assign(this.queryForm,this.queryFormDf)
      },
      //高级查询
      querySure(){
        for(let key in this.queryForm){
          this.searchForm[key] = this.queryForm[key]
        }
        this.searchForm.pageNo = 1
        this.$refs.tzPlanListedTargetQuerySearch.close()
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        const {
          data: { list, total },
        } = await tzPlanListedTargetGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>